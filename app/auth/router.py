from fastapi import APIRouter, Request
from fastapi.responses import RedirectResponse, HTMLResponse
from starlette.config import Config
from authlib.integrations.starlette_client import OAuth, OAuthError

from app.core.config import settings

router = APIRouter()

# Configure OAuth with Google
config = Config(environ={
    'GOOGLE_CLIENT_ID': settings.GOOGLE_CLIENT_ID,
    'GOOGLE_CLIENT_SECRET': settings.GOOGLE_CLIENT_SECRET,
})

oauth = OAuth(config)
# OpenID Connect endpoints for Google
oauth.register(
    name='google',
    server_metadata_url='https://accounts.google.com/.well-known/openid-configuration',
    client_id=settings.GOOGLE_CLIENT_ID,
    client_secret=settings.GOOGLE_CLIENT_SECRET,
    client_kwargs={'scope': 'openid email profile'},
)


@router.get('/login', response_class=HTMLResponse)
async def login(request: Request):
    # If already logged, go home
    if request.session.get('user'):
        return RedirectResponse('/')
    redirect_uri = request.url_for('auth')
    return await oauth.google.authorize_redirect(request, redirect_uri)


@router.get('/auth/callback')
async def auth(request: Request):
    try:
        token = await oauth.google.authorize_access_token(request)
    except OAuthError as e:
        # On error, redirect to login with message
        return RedirectResponse('/login')

    userinfo = token.get('userinfo')
    if not userinfo:
        # For some providers, need to fetch explicitly
        userinfo = await oauth.google.parse_id_token(request, token)

    # Store minimal user info in session
    request.session['user'] = {
        'sub': userinfo.get('sub'),
        'email': userinfo.get('email'),
        'name': userinfo.get('name'),
        'picture': userinfo.get('picture'),
    }
    return RedirectResponse('/')


@router.get('/logout')
async def logout(request: Request):
    request.session.clear()
    return RedirectResponse('/login')

